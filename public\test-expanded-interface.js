/**
 * Test Suite for Expanded Interactive Interface
 * Tests all interactive elements with enhanced sizing and Casio theme
 */

// Test state tracking
const testResults = {
    numberLine: 'pending',
    dragDrop: 'pending',
    calculator: 'pending',
    coordinate: 'pending'
};

// Test Number Line Slider
function testNumberLineSlider() {
    const container = document.getElementById('number-line-slider');
    const resultsArea = document.getElementById('numberline-results');
    const statusIndicator = document.getElementById('numberline-status');
    
    container.classList.remove('hidden');
    resultsArea.innerHTML = '<p><strong>Testing Number Line Slider...</strong></p>';
    
    // Test container sizing
    const containerRect = container.getBoundingClientRect();
    const trackElement = document.getElementById('number-line-track');
    const trackRect = trackElement.getBoundingClientRect();
    
    let testsPassed = 0;
    let totalTests = 4;
    
    // Test 1: Container has adequate height
    if (containerRect.height >= 200) {
        resultsArea.innerHTML += '<p>✅ Container height adequate: ' + Math.round(containerRect.height) + 'px</p>';
        testsPassed++;
    } else {
        resultsArea.innerHTML += '<p>❌ Container height too small: ' + Math.round(containerRect.height) + 'px</p>';
    }
    
    // Test 2: Track has enhanced width
    if (trackRect.width >= 500) {
        resultsArea.innerHTML += '<p>✅ Track width enhanced: ' + Math.round(trackRect.width) + 'px</p>';
        testsPassed++;
    } else {
        resultsArea.innerHTML += '<p>❌ Track width not enhanced: ' + Math.round(trackRect.width) + 'px</p>';
    }
    
    // Test 3: Handle interaction
    const handle = document.getElementById('number-line-handle');
    if (handle && handle.style.cursor !== 'not-allowed') {
        resultsArea.innerHTML += '<p>✅ Handle is interactive</p>';
        testsPassed++;
    } else {
        resultsArea.innerHTML += '<p>❌ Handle interaction issue</p>';
    }
    
    // Test 4: Casio theme colors applied
    const computedStyle = window.getComputedStyle(container);
    if (computedStyle.borderColor.includes('21, 71, 187') || computedStyle.borderColor.includes('#1547bb')) {
        resultsArea.innerHTML += '<p>✅ Casio theme colors applied</p>';
        testsPassed++;
    } else {
        resultsArea.innerHTML += '<p>❌ Casio theme colors not detected</p>';
    }
    
    // Update test result
    const passed = testsPassed === totalTests;
    testResults.numberLine = passed ? 'pass' : 'fail';
    statusIndicator.className = `status-indicator status-${passed ? 'pass' : 'fail'}`;
    
    resultsArea.innerHTML += `<p><strong>Result: ${testsPassed}/${totalTests} tests passed</strong></p>`;
    updateOverallResults();
}

// Test Drag and Drop
function testDragDrop() {
    const container = document.getElementById('drag-drop-matching');
    const resultsArea = document.getElementById('dragdrop-results');
    const statusIndicator = document.getElementById('dragdrop-status');
    
    container.classList.remove('hidden');
    resultsArea.innerHTML = '<p><strong>Testing Drag and Drop Interface...</strong></p>';
    
    const containerRect = container.getBoundingClientRect();
    const matchingContainer = container.querySelector('.matching-container');
    const matchingRect = matchingContainer.getBoundingClientRect();
    
    let testsPassed = 0;
    let totalTests = 4;
    
    // Test 1: Container has adequate height
    if (containerRect.height >= 280) {
        resultsArea.innerHTML += '<p>✅ Container height adequate: ' + Math.round(containerRect.height) + 'px</p>';
        testsPassed++;
    } else {
        resultsArea.innerHTML += '<p>❌ Container height too small: ' + Math.round(containerRect.height) + 'px</p>';
    }
    
    // Test 2: Matching container has enhanced spacing
    if (matchingRect.height >= 250) {
        resultsArea.innerHTML += '<p>✅ Matching container height enhanced: ' + Math.round(matchingRect.height) + 'px</p>';
        testsPassed++;
    } else {
        resultsArea.innerHTML += '<p>❌ Matching container height not enhanced: ' + Math.round(matchingRect.height) + 'px</p>';
    }
    
    // Test 3: Draggable items are functional
    const draggableItems = container.querySelectorAll('.draggable-item');
    if (draggableItems.length > 0 && draggableItems[0].draggable) {
        resultsArea.innerHTML += '<p>✅ Draggable items are functional</p>';
        testsPassed++;
    } else {
        resultsArea.innerHTML += '<p>❌ Draggable items not functional</p>';
    }
    
    // Test 4: Drop zones are present
    const dropZones = container.querySelectorAll('.drop-zone');
    if (dropZones.length > 0) {
        resultsArea.innerHTML += '<p>✅ Drop zones are present</p>';
        testsPassed++;
    } else {
        resultsArea.innerHTML += '<p>❌ Drop zones not found</p>';
    }
    
    const passed = testsPassed === totalTests;
    testResults.dragDrop = passed ? 'pass' : 'fail';
    statusIndicator.className = `status-indicator status-${passed ? 'pass' : 'fail'}`;
    
    resultsArea.innerHTML += `<p><strong>Result: ${testsPassed}/${totalTests} tests passed</strong></p>`;
    updateOverallResults();
}

// Test Visual Calculator
function testCalculator() {
    const container = document.getElementById('visual-calculator');
    const resultsArea = document.getElementById('calculator-results');
    const statusIndicator = document.getElementById('calculator-status');
    
    container.classList.remove('hidden');
    resultsArea.innerHTML = '<p><strong>Testing Visual Calculator...</strong></p>';
    
    // Create calculator keypad for testing
    createCalculatorKeypad();
    
    const containerRect = container.getBoundingClientRect();
    const calcContainer = container.querySelector('.calculator-container');
    const calcRect = calcContainer.getBoundingClientRect();
    
    let testsPassed = 0;
    let totalTests = 4;
    
    // Test 1: Container has adequate height
    if (containerRect.height >= 300) {
        resultsArea.innerHTML += '<p>✅ Container height adequate: ' + Math.round(containerRect.height) + 'px</p>';
        testsPassed++;
    } else {
        resultsArea.innerHTML += '<p>❌ Container height too small: ' + Math.round(containerRect.height) + 'px</p>';
    }
    
    // Test 2: Calculator container enhanced
    if (calcRect.height >= 280) {
        resultsArea.innerHTML += '<p>✅ Calculator container enhanced: ' + Math.round(calcRect.height) + 'px</p>';
        testsPassed++;
    } else {
        resultsArea.innerHTML += '<p>❌ Calculator container not enhanced: ' + Math.round(calcRect.height) + 'px</p>';
    }
    
    // Test 3: Display is present
    const display = container.querySelector('.calculator-display');
    if (display) {
        resultsArea.innerHTML += '<p>✅ Calculator display present</p>';
        testsPassed++;
    } else {
        resultsArea.innerHTML += '<p>❌ Calculator display not found</p>';
    }
    
    // Test 4: Keypad buttons are functional
    const buttons = container.querySelectorAll('.calc-btn');
    if (buttons.length > 0) {
        resultsArea.innerHTML += '<p>✅ Calculator buttons present: ' + buttons.length + ' buttons</p>';
        testsPassed++;
    } else {
        resultsArea.innerHTML += '<p>❌ Calculator buttons not found</p>';
    }
    
    const passed = testsPassed === totalTests;
    testResults.calculator = passed ? 'pass' : 'fail';
    statusIndicator.className = `status-indicator status-${passed ? 'pass' : 'fail'}`;
    
    resultsArea.innerHTML += `<p><strong>Result: ${testsPassed}/${totalTests} tests passed</strong></p>`;
    updateOverallResults();
}

// Test Coordinate Plotting
function testCoordinatePlotting() {
    const container = document.getElementById('coordinate-plotting');
    const resultsArea = document.getElementById('coordinate-results');
    const statusIndicator = document.getElementById('coordinate-status');
    
    container.classList.remove('hidden');
    resultsArea.innerHTML = '<p><strong>Testing Coordinate Plotting...</strong></p>';
    
    const containerRect = container.getBoundingClientRect();
    const canvas = document.getElementById('coordinate-canvas');
    const canvasRect = canvas.getBoundingClientRect();
    
    let testsPassed = 0;
    let totalTests = 4;
    
    // Test 1: Container has adequate height
    if (containerRect.height >= 350) {
        resultsArea.innerHTML += '<p>✅ Container height adequate: ' + Math.round(containerRect.height) + 'px</p>';
        testsPassed++;
    } else {
        resultsArea.innerHTML += '<p>❌ Container height too small: ' + Math.round(containerRect.height) + 'px</p>';
    }
    
    // Test 2: Canvas has enhanced size
    if (canvasRect.width >= 400 && canvasRect.height >= 300) {
        resultsArea.innerHTML += '<p>✅ Canvas size enhanced: ' + Math.round(canvasRect.width) + 'x' + Math.round(canvasRect.height) + 'px</p>';
        testsPassed++;
    } else {
        resultsArea.innerHTML += '<p>❌ Canvas size not enhanced: ' + Math.round(canvasRect.width) + 'x' + Math.round(canvasRect.height) + 'px</p>';
    }
    
    // Test 3: Canvas is interactive
    if (canvas && canvas.getContext) {
        resultsArea.innerHTML += '<p>✅ Canvas is interactive</p>';
        testsPassed++;
    } else {
        resultsArea.innerHTML += '<p>❌ Canvas not interactive</p>';
    }
    
    // Test 4: Coordinate display present
    const display = document.getElementById('coordinate-display');
    if (display) {
        resultsArea.innerHTML += '<p>✅ Coordinate display present</p>';
        testsPassed++;
    } else {
        resultsArea.innerHTML += '<p>❌ Coordinate display not found</p>';
    }
    
    const passed = testsPassed === totalTests;
    testResults.coordinate = passed ? 'pass' : 'fail';
    statusIndicator.className = `status-indicator status-${passed ? 'pass' : 'fail'}`;
    
    resultsArea.innerHTML += `<p><strong>Result: ${testsPassed}/${totalTests} tests passed</strong></p>`;
    updateOverallResults();
}

// Helper function to create calculator keypad
function createCalculatorKeypad() {
    const keypadContainer = document.getElementById('calculator-keypad');
    if (!keypadContainer) return;
    
    const buttons = [
        'C', '±', '%', '÷',
        '7', '8', '9', '×',
        '4', '5', '6', '-',
        '1', '2', '3', '+',
        '0', '.', '='
    ];
    
    keypadContainer.innerHTML = '';
    buttons.forEach(btn => {
        const button = document.createElement('button');
        button.className = 'calc-btn';
        button.textContent = btn;
        button.onclick = () => handleCalculatorInput(btn);
        keypadContainer.appendChild(button);
    });
}

// Handle calculator input
function handleCalculatorInput(value) {
    const display = document.getElementById('current-display');
    if (display) {
        if (value === 'C') {
            display.textContent = '0';
        } else if (value === '=') {
            // Simple calculation for testing
            display.textContent = 'Result';
        } else {
            if (display.textContent === '0') {
                display.textContent = value;
            } else {
                display.textContent += value;
            }
        }
    }
}

// Reset functions
function resetNumberLine() {
    const handle = document.getElementById('number-line-handle');
    const value = document.getElementById('number-line-value');
    if (handle) handle.style.left = '50%';
    if (value) value.textContent = '0';
}

function resetDragDrop() {
    const draggableItems = document.querySelectorAll('.draggable-item');
    const dropZones = document.querySelectorAll('.drop-zone');
    
    draggableItems.forEach(item => {
        item.style.transform = '';
        item.style.position = '';
    });
    
    dropZones.forEach(zone => {
        zone.innerHTML = '<div class="drop-zone-label">Equals 5</div><div class="drop-zone-content">Drop items that equal 5</div>';
    });
}

function resetCalculator() {
    const display = document.getElementById('current-display');
    if (display) display.textContent = '0';
}

function resetCoordinatePlotting() {
    const canvas = document.getElementById('coordinate-canvas');
    if (canvas) {
        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);
    }
}

// Check functions (placeholder implementations)
function checkNumberLine() {
    alert('Number line value checked! This is a test implementation.');
}

function checkDragDrop() {
    alert('Drag and drop matching checked! This is a test implementation.');
}

function checkCalculator() {
    alert('Calculator result checked! This is a test implementation.');
}

function checkCoordinatePlotting() {
    alert('Coordinate plotting checked! This is a test implementation.');
}

// Update overall results
function updateOverallResults() {
    document.getElementById('summary-numberline').textContent = testResults.numberLine;
    document.getElementById('summary-dragdrop').textContent = testResults.dragDrop;
    document.getElementById('summary-calculator').textContent = testResults.calculator;
    document.getElementById('summary-coordinate').textContent = testResults.coordinate;
}

// Run all tests
function runAllTests() {
    testNumberLineSlider();
    setTimeout(() => testDragDrop(), 500);
    setTimeout(() => testCalculator(), 1000);
    setTimeout(() => testCoordinatePlotting(), 1500);
}

// Test responsiveness
function checkResponsiveness() {
    const resultsArea = document.getElementById('overall-results');
    const currentWidth = window.innerWidth;
    
    resultsArea.innerHTML += `<p><strong>Responsiveness Test:</strong></p>`;
    resultsArea.innerHTML += `<p>Current viewport width: ${currentWidth}px</p>`;
    
    if (currentWidth <= 480) {
        resultsArea.innerHTML += `<p>📱 Small mobile layout detected</p>`;
    } else if (currentWidth <= 768) {
        resultsArea.innerHTML += `<p>📱 Mobile layout detected</p>`;
    } else if (currentWidth <= 1024) {
        resultsArea.innerHTML += `<p>📱 Tablet layout detected</p>`;
    } else {
        resultsArea.innerHTML += `<p>🖥️ Desktop layout detected</p>`;
    }
    
    resultsArea.innerHTML += `<p>✅ Responsive design is working correctly for current viewport</p>`;
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    console.log('Expanded Interface Test Suite loaded');
    updateOverallResults();
});
