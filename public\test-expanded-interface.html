<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Expanded Interactive Interface</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="mathInteractive.css">
    <link rel="stylesheet" href="interactive-questions.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 40px;
            color: #121c41;
        }
        
        .test-section {
            margin-bottom: 50px;
            padding: 20px;
            border: 2px solid #1547bb;
            border-radius: 12px;
            background: #f8f9ff;
        }
        
        .test-title {
            color: #121c41;
            font-size: 1.5rem;
            margin-bottom: 20px;
            font-weight: 600;
        }
        
        .test-description {
            color: #666;
            margin-bottom: 20px;
            font-size: 1rem;
        }
        
        .test-button {
            background: #1547bb;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            background: #121c41;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(21, 71, 187, 0.3);
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }
        
        .status-pass { background: #28a745; }
        .status-fail { background: #dc3545; }
        .status-pending { background: #ffc107; }
        
        .results-area {
            margin-top: 20px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e0e6ff;
            min-height: 100px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>Expanded Interactive Interface Test Suite</h1>
            <p>Testing the enhanced mathematics assessment interactive elements</p>
        </div>

        <!-- Number Line Slider Test -->
        <div class="test-section">
            <h2 class="test-title">Number Line Slider Test <span id="numberline-status" class="status-indicator status-pending"></span></h2>
            <p class="test-description">Testing the expanded number line slider with enhanced sizing and Casio theme colors.</p>
            <button class="test-button" onclick="testNumberLineSlider()">Test Number Line</button>
            
            <div id="number-line-slider" class="interactive-question hidden">
                <div class="number-line-container">
                    <div class="number-line-labels" id="number-line-labels">
                        <span>-10</span>
                        <span>0</span>
                        <span>10</span>
                    </div>
                    <div class="number-line-track" id="number-line-track">
                        <div class="number-line-handle" id="number-line-handle" tabindex="0" role="slider"></div>
                    </div>
                    <div class="number-line-value" id="number-line-value">0</div>
                </div>
                <p class="input-hint">Drag the handle to select your answer</p>
                <div class="interactive-controls">
                    <button class="reset-btn" onclick="resetNumberLine()">
                        <span class="btn-icon">↻</span> Reset
                    </button>
                    <button class="check-btn" onclick="checkNumberLine()">
                        <span class="btn-icon">✓</span> Check
                    </button>
                </div>
            </div>
            
            <div class="results-area" id="numberline-results">
                Click "Test Number Line" to begin testing...
            </div>
        </div>

        <!-- Drag and Drop Test -->
        <div class="test-section">
            <h2 class="test-title">Drag and Drop Matching Test <span id="dragdrop-status" class="status-indicator status-pending"></span></h2>
            <p class="test-description">Testing the expanded drag-and-drop interface with enhanced container sizing.</p>
            <button class="test-button" onclick="testDragDrop()">Test Drag & Drop</button>
            
            <div id="drag-drop-matching" class="interactive-question hidden">
                <div class="matching-container">
                    <div class="draggable-items">
                        <h5>Drag these items:</h5>
                        <div class="draggable-item" draggable="true" data-value="2+3">2 + 3</div>
                        <div class="draggable-item" draggable="true" data-value="4+1">4 + 1</div>
                        <div class="draggable-item" draggable="true" data-value="3+2">3 + 2</div>
                    </div>
                    <div class="drop-zones">
                        <h5>Drop here:</h5>
                        <div class="drop-zone" data-target="5">
                            <div class="drop-zone-label">Equals 5</div>
                            <div class="drop-zone-content">Drop items that equal 5</div>
                        </div>
                    </div>
                </div>
                <div class="interactive-controls">
                    <button class="reset-btn" onclick="resetDragDrop()">
                        <span class="btn-icon">↻</span> Reset
                    </button>
                    <button class="check-btn" onclick="checkDragDrop()">
                        <span class="btn-icon">✓</span> Check
                    </button>
                </div>
            </div>
            
            <div class="results-area" id="dragdrop-results">
                Click "Test Drag & Drop" to begin testing...
            </div>
        </div>

        <!-- Visual Calculator Test -->
        <div class="test-section">
            <h2 class="test-title">Visual Calculator Test <span id="calculator-status" class="status-indicator status-pending"></span></h2>
            <p class="test-description">Testing the expanded visual calculator with enhanced Casio-inspired design.</p>
            <button class="test-button" onclick="testCalculator()">Test Calculator</button>
            
            <div id="visual-calculator" class="interactive-question hidden">
                <div class="calculator-container">
                    <div class="calculator-display">
                        <div class="target-calculation">Calculate: 15 + 27</div>
                        <div class="current-display" id="current-display">0</div>
                    </div>
                    <div class="calculator-keypad" id="calculator-keypad">
                        <!-- Keypad will be generated by JavaScript -->
                    </div>
                </div>
                <div class="interactive-controls">
                    <button class="reset-btn" onclick="resetCalculator()">
                        <span class="btn-icon">↻</span> Reset
                    </button>
                    <button class="check-btn" onclick="checkCalculator()">
                        <span class="btn-icon">✓</span> Check
                    </button>
                </div>
            </div>
            
            <div class="results-area" id="calculator-results">
                Click "Test Calculator" to begin testing...
            </div>
        </div>

        <!-- Coordinate Plotting Test -->
        <div class="test-section">
            <h2 class="test-title">Coordinate Plotting Test <span id="coordinate-status" class="status-indicator status-pending"></span></h2>
            <p class="test-description">Testing the expanded coordinate plotting interface with enhanced canvas sizing.</p>
            <button class="test-button" onclick="testCoordinatePlotting()">Test Coordinate Plotting</button>
            
            <div id="coordinate-plotting" class="interactive-question hidden">
                <div class="coordinate-container">
                    <canvas id="coordinate-canvas" class="interactive-canvas" width="500" height="400"></canvas>
                    <div class="coordinate-display" id="coordinate-display">Click on the grid to plot points</div>
                </div>
                <div class="interactive-controls">
                    <button class="reset-btn" onclick="resetCoordinatePlotting()">
                        <span class="btn-icon">↻</span> Reset
                    </button>
                    <button class="check-btn" onclick="checkCoordinatePlotting()">
                        <span class="btn-icon">✓</span> Check
                    </button>
                </div>
            </div>
            
            <div class="results-area" id="coordinate-results">
                Click "Test Coordinate Plotting" to begin testing...
            </div>
        </div>

        <!-- Overall Test Results -->
        <div class="test-section">
            <h2 class="test-title">Overall Test Results</h2>
            <div class="results-area" id="overall-results">
                <p><strong>Test Summary:</strong></p>
                <ul id="test-summary">
                    <li>Number Line Slider: <span id="summary-numberline">Pending</span></li>
                    <li>Drag & Drop Matching: <span id="summary-dragdrop">Pending</span></li>
                    <li>Visual Calculator: <span id="summary-calculator">Pending</span></li>
                    <li>Coordinate Plotting: <span id="summary-coordinate">Pending</span></li>
                </ul>
                <button class="test-button" onclick="runAllTests()">Run All Tests</button>
                <button class="test-button" onclick="checkResponsiveness()">Test Responsiveness</button>
            </div>
        </div>
    </div>

    <script src="test-expanded-interface.js"></script>
</body>
</html>
